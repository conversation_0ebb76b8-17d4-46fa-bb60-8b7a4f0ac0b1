/**
 * Bridge Script (Content Script - Isolated World)
 *
 * This script acts as a bridge between the main world (interceptor) and
 * the extension's background service worker.
 */

// Logger will be created inline to avoid module import issues

let platformModulePath = null;
let debugLoggingEnabled = false;
let interceptorReady = false;
let serviceWorkerReady = false;
let initializationRetryCount = 0;
let logger = {
    log: (...args) => {
        if (debugLoggingEnabled) {
            console.log('LLMLog:', ...args);
        }
    },
    error: (...args) => {
        if (debugLoggingEnabled) {
            console.error('LLMLog:', ...args);
        }
    },
    warn: (...args) => {
        if (debugLoggingEnabled) {
            console.warn('LLMLog:', ...args);
        }
    },
    info: (...args) => {
        if (debugLoggingEnabled) {
            console.info('LLMLog:', ...args);
        }
    },
    setDebugMode: (mode) => {
        debugLoggingEnabled = mode;
    }
};
const MAX_RETRY_COUNT = 5;
const RETRY_DELAY = 1000; // 1 second

function sendInitMessageToInterceptor() {
    // Only proceed if both the path is available and the interceptor is ready
    if (!platformModulePath || !interceptorReady) {
        if (debugLoggingEnabled) logger.log('LLMLog Bridge: Not ready to send init message.', {
            platformModulePath: !!platformModulePath,
            interceptorReady
        });
        return;
    }
    
    if (debugLoggingEnabled) logger.log('LLMLog Bridge: Sending init message to interceptor.');
    
    const fullModuleUrl = chrome.runtime.getURL(platformModulePath);
    const loggerUrl = chrome.runtime.getURL('modules/logger.js');
    
    // Send the correct message type that the interceptor is expecting
    window.postMessage({
        type: 'LLMLOG_INIT',
        payload: {
            modulePath: fullModuleUrl,
            loggerPath: loggerUrl,
            debugMode: debugLoggingEnabled
        }
    }, window.location.origin);
}

// Robust sendMessage wrapper with enhanced error handling
function sendMessageRobust(message, callback) {
    if (debugLoggingEnabled) {
        logger.log('LLMLog Bridge: Sending message:', message);
    }
    
    try {
        const promise = chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
                const error = chrome.runtime.lastError;
                if (error.message.includes('Extension context invalidated')) {
                    if (debugLoggingEnabled) logger.warn('LLMLog Bridge: Extension context invalidated. Service worker likely restarted. This is expected.');
                } else if (error.message.includes('Receiving end does not exist')) {
                    logger.warn('LLMLog Bridge: Service worker not ready. Message:', message.namespace + '.' + message.action);
                } else if (error.message.includes('back/forward cache')) {
                    // Handle back/forward cache issue gracefully
                    if (debugLoggingEnabled) logger.warn('LLMLog Bridge: Page moved to back/forward cache, message channel closed. This is expected.');
                } else if (error.message.includes('message channel is closed')) {
                    // Handle closed message channel gracefully
                    if (debugLoggingEnabled) logger.warn('LLMLog Bridge: Message channel closed. This can happen during navigation.');
                } else {
                    logger.error('LLMLog Bridge: Runtime error:', error.message, 'Message:', message);
                }
                return;
            }
            
            if (debugLoggingEnabled) {
                logger.log('LLMLog Bridge: Received response:', response);
            }
            
            if (callback) {
                callback(response);
            }
        });
        
        if (promise && typeof promise.catch === 'function') {
            promise.catch(error => {
                if (error.message.includes('Extension context invalidated')) {
                    if (debugLoggingEnabled) logger.warn('LLMLog Bridge: Extension context invalidated. Service worker likely restarted. This is expected.');
                } else if (error.message.includes('Receiving end does not exist')) {
                    logger.warn('LLMLog Bridge: Service worker not ready for promise. Message:', message.namespace + '.' + message.action);
                } else {
                    logger.error('LLMLog Bridge: Promise error:', error, 'Message:', message);
                }
            });
        }
    } catch (error) {
        if (error.message.includes('Extension context invalidated')) {
            if (debugLoggingEnabled) logger.warn('LLMLog Bridge: Extension context invalidated. Service worker likely restarted. This is expected.');
        } else if (error.message.includes('Receiving end does not exist')) {
            logger.warn('LLMLog Bridge: Service worker not ready for catch. Message:', message.namespace + '.' + message.action);
        } else {
            logger.error('LLMLog Bridge: Failed to send message:', error, 'Message:', message);
        }
    }
}

// Listen for messages from the interceptor (main world)
window.addEventListener('message', (event) => {
    if (event.source !== window) return;
    
    // Handle conversation data from the interceptor
    if (event.data.type === 'LLMLOG_CONVERSATION') {
        if (debugLoggingEnabled) logger.log('LLMLog Bridge: Received conversation from interceptor.', event.data.payload);
        
        sendMessageRobust({
            namespace: 'database',
            action: 'saveConversation',
            payload: event.data.payload
        }, response => {
            if (debugLoggingEnabled) {
                if (response && response.status === 'success') {
                    logger.log('LLMLog Bridge: Conversation saved successfully.', response.data);
                } else if (response && response.status === 'error') {
                    logger.error('LLMLog Bridge: Error saving conversation:', response.message);
                } else {
                    logger.log('LLMLog Bridge: Save confirmation received.', response);
                }
            }
        });
    }
    
    // Handle ready signal from the interceptor
    if (event.data.type === 'LLMLOG_INTERCEPTOR_READY') {
        if (debugLoggingEnabled) logger.log('LLMLog Bridge: Interceptor is ready.');
        interceptorReady = true;
        sendInitMessageToInterceptor(); // Attempt to send the init message now
    }
});

// Keep the service worker alive
let serviceWorkerPort = null;
let pageLoadTime = Date.now();

function connectToServiceWorker() {
    serviceWorkerPort = chrome.runtime.connect({ name: 'llmlog-bridge' });
    if (debugLoggingEnabled) logger.log('LLMLog Bridge: Connecting to service worker...');
    
    serviceWorkerPort.onDisconnect.addListener(() => {
        const timeSincePageLoad = Date.now() - pageLoadTime;
        const isRecentPageLoad = timeSincePageLoad < 5000; // Within 5 seconds of page load
        
        // Only log warning if it's not shortly after page load (which indicates extension reload)
        if (!isRecentPageLoad && debugLoggingEnabled) {
            // logger.warn('LLMLog Bridge: Service worker port disconnected. Reconnecting...');
        } else if (debugLoggingEnabled) {
            logger.log('LLMLog Bridge: Reconnecting to service worker after page reload...');
        }
        
        serviceWorkerPort = null; // Clear the old port
        
        // Add a small delay before reconnecting to avoid spamming
        setTimeout(connectToServiceWorker, 1000);
    });
}


// Request platform configuration with retry logic
function requestPlatformConfig(platformName, retryCount = 0) {
    if (debugLoggingEnabled) logger.log(`LLMLog Bridge: Requesting platform config for ${platformName} (attempt ${retryCount + 1})`);
    
    sendMessageRobust({
        namespace: 'capture',
        action: 'getPlatformConfig',
        payload: { platform: platformName }
    }, (response) => {
        // sendMessageRobust already handles chrome.runtime.lastError
        // Check if response indicates an error or is null/undefined
        if (!response || (response && response.status === 'error')) {
            logger.error('LLMLog Bridge: Error getting platform module path:', response?.message || 'No response received');
            
            // Retry if we haven't exceeded max retries
            if (retryCount < MAX_RETRY_COUNT) {
                setTimeout(() => {
                    requestPlatformConfig(platformName, retryCount + 1);
                }, RETRY_DELAY * (retryCount + 1)); // Exponential backoff
            }
            return;
        }
        
        // Handle both wrapped and unwrapped responses
        let modulePath = null;
        if (response) {
            if (response.modulePath) {
                // Direct response format
                modulePath = response.modulePath;
            } else if (response.status === 'success' && response.data && response.data.modulePath) {
                // Wrapped response format
                modulePath = response.data.modulePath;
            } else if (response.status === 'error') {
                logger.error('LLMLog Bridge: Error from service worker:', response.message);
                
                // Retry on error if we haven't exceeded max retries
                if (retryCount < MAX_RETRY_COUNT) {
                    setTimeout(() => {
                        requestPlatformConfig(platformName, retryCount + 1);
                    }, RETRY_DELAY * (retryCount + 1));
                }
                return;
            }
        }
        
        if (modulePath) {
            platformModulePath = modulePath;
            if (debugLoggingEnabled) logger.log('LLMLog Bridge: Platform module path received and stored.', platformModulePath);
            sendInitMessageToInterceptor(); // Attempt to send the init message now
        } else {
            logger.error('LLMLog Bridge: Did not receive a valid platform module path.', response);
            
            // Retry if we haven't exceeded max retries
            if (retryCount < MAX_RETRY_COUNT) {
                setTimeout(() => {
                    requestPlatformConfig(platformName, retryCount + 1);
                }, RETRY_DELAY * (retryCount + 1));
            }
        }
    });
}

// Determine the platform and fetch the config from the service worker
async function initializeBridge() {
    // First get debug settings
    sendMessageRobust({
        namespace: 'settings',
        action: 'get',
        payload: { key: 'debugLoggingEnabled' }
    }, (response) => {
        // Handle wrapped response format
        if (response && response.status === 'success') {
            debugLoggingEnabled = response.data;
            // Update logger with new debug mode
            logger.setDebugMode(debugLoggingEnabled);
        } else if (typeof response === 'boolean') {
            debugLoggingEnabled = response;
            // Update logger with new debug mode
            logger.setDebugMode(debugLoggingEnabled);
        }
        
        if (debugLoggingEnabled) logger.log('LLMLog Bridge: Debug logging enabled');
    });
    
    connectToServiceWorker(); // Establish the persistent connection
    
    // Detect platform
    let platformName = null;
    if (window.location.hostname.includes('chat.openai') || window.location.hostname.includes('chatgpt')) {
        platformName = 'chatgpt';
    } else if (window.location.hostname.includes('gemini.google')) {
        platformName = 'gemini';
    } else if (window.location.hostname.includes('claude.ai')) {
        platformName = 'claude';
    }
    
    if (platformName) {
        if (debugLoggingEnabled) logger.log(`LLMLog Bridge: Platform detected: ${platformName}. Requesting config.`);
        
        // Add a small delay to ensure service worker is ready
        setTimeout(() => {
            requestPlatformConfig(platformName);
        }, 500);
    } else {
        if (debugLoggingEnabled) logger.log('LLMLog Bridge: No supported platform detected for hostname:', window.location.hostname);
    }
}

// Diagnostic function for troubleshooting
function diagnosticInfo() {
    return {
        hostname: window.location.hostname,
        platformModulePath,
        debugLoggingEnabled,
        interceptorReady,
        serviceWorkerReady,
        initializationRetryCount,
        serviceWorkerPortConnected: !!serviceWorkerPort,
        timestamp: new Date().toISOString()
    };
}

// Make diagnostic function available globally for debugging
window.llmlogDiagnostic = diagnosticInfo;

initializeBridge();

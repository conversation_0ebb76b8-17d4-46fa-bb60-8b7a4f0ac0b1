/**
 * Global Logger Manager
 * 
 * This module provides centralized logger management with real-time debug mode updates
 * across all extension contexts (popup, options, content scripts, service worker).
 */

import { createLogger } from './logger.js';
import { getSetting } from './settings.js';

class LoggerManager {
    constructor() {
        this.loggers = new Set();
        this.debugMode = false;
        this.initialized = false;
    }

    /**
     * Initialize the logger manager
     */
    async initialize() {
        if (this.initialized) return;

        // Get initial debug setting
        this.debugMode = await getSetting('debugLoggingEnabled');
        
        // Listen for storage changes to update all loggers in real-time
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'local' && changes.debugLoggingEnabled) {
                this.updateAllLoggers(changes.debugLoggingEnabled.newValue);
            }
        });

        this.initialized = true;
    }

    /**
     * Create a new logger instance and register it for updates
     */
    async createManagedLogger() {
        await this.initialize();
        
        const logger = createLogger(this.debugMode);
        this.loggers.add(logger);
        
        return logger;
    }

    /**
     * Update debug mode for all registered loggers
     */
    updateAllLoggers(newDebugMode) {
        this.debugMode = newDebugMode;
        
        for (const logger of this.loggers) {
            logger.setDebugMode(newDebugMode);
        }

        // Broadcast update to content scripts via message passing
        this.broadcastDebugModeUpdate(newDebugMode);
    }

    /**
     * Broadcast debug mode update to content scripts
     */
    broadcastDebugModeUpdate(debugMode) {
        // Only available in service worker context
        if (typeof chrome !== 'undefined' && chrome.tabs && chrome.tabs.query) {
            chrome.tabs.query({}, (tabs) => {
                tabs.forEach(tab => {
                    if (tab.id) {
                        chrome.tabs.sendMessage(tab.id, {
                            type: 'LLMLOG_DEBUG_MODE_UPDATE',
                            debugMode: debugMode
                        }).catch(() => {
                            // Ignore errors for tabs that don't have content scripts
                        });
                    }
                });
            });
        }
    }

    /**
     * Remove a logger from management (for cleanup)
     */
    removeLogger(logger) {
        this.loggers.delete(logger);
    }

    /**
     * Get current debug mode
     */
    getDebugMode() {
        return this.debugMode;
    }
}

// Global instance
const loggerManager = new LoggerManager();

/**
 * Create a managed logger that will automatically update when debug mode changes
 */
export async function createManagedLogger() {
    return await loggerManager.createManagedLogger();
}

/**
 * Get the global logger manager instance
 */
export function getLoggerManager() {
    return loggerManager;
}

/**
 * Initialize logger manager (should be called in service worker)
 */
export async function initializeLoggerManager() {
    await loggerManager.initialize();
}
